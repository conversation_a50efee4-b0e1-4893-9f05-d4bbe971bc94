import type { MarketStep1Params } from '@/api/MarketApi'
import type { TrendStep1Params } from '@/api/TrendApi'
import type { WorkflowStep } from '../../ChatV2/components/ChatWorkflow'

import type { MdToCodePreviewType, MessageStoreType, ReportStoreType, StateStoreType, StepStateType, TaskStoreType } from '../stores'
import type { ParsedStreamData } from '../stores/cozeStreamApi'
import { FileAPI } from '@/api/FileAPI'
import { AnimateShow } from '@/components/Animate/AnimateShow'
import { LoadingIcon } from '@/components/Loading/LoadingIcon'
import { useBindWinEvent } from '@/hooks'
import { cn } from '@/utils'
import classNames from 'clsx'
import { motion } from 'framer-motion'

import { ChevronDown, Sparkles } from 'lucide-react'
import { memo, useCallback, useEffect, useInsertionEffect, useRef, useState } from 'react'
import ChatWorkflow from '../../ChatV2/components/ChatWorkflow'
import TopBar from '../../ChatV2/components/TopBar'
import { DistributionEvent, eventBus } from '../constants'

import { trendAg } from '../stores'
import { addReportItem, createCardMessage, removeMessage } from '../stores/create'
import { ChatHistory } from './ChatHistory'
import { MessageItem } from './MessageItem'
import { ReportPreview } from './ReportComponents/ReportPreview'
import { TextDisplayWithPagination } from './TextDisplayWithPagination'
import { ThinkingStream } from './ThinkingStream'

/** Strategy卡片渲染组件 - 专门渲染Strategy卡片 */
const StrategyCardRenderer = memo<{
  messageStore: any
  stateStore: any
}>(({ messageStore, stateStore }) => {
  const { messages } = messageStore.useAndDispose()

  /** 找到Strategy卡片 */
  const strategyCard = useMemo(() => {
    return messages.find((msg: any) => msg.meta?.isStrategyCard && msg.type === 'card')
  }, [messages])

  if (!strategyCard) {
    return null
  }

  return (
    <motion.div
      initial={ { opacity: 0, y: 20 } }
      animate={ { opacity: 1, y: 0 } }
      transition={ { duration: 0.5 } }
      className="mt-4"
    >
      <MessageItem
        message={ strategyCard }
        onDelete={ () => {} }
        className="w-full"
        stateStore={ stateStore }
      />
    </motion.div>
  )
})

StrategyCardRenderer.displayName = 'StrategyCardRenderer'

/** Implementation卡片渲染组件 - 专门渲染Implementation卡片 */
const ImplementationCardRenderer = memo<{
  messageStore: any
  stateStore: any
  className?: string
}>(({ messageStore, stateStore, className }) => {
  const { messages } = messageStore.useAndDispose()

  /** 找到Implementation卡片 */
  const implementationCard = useMemo(() => {
    return messages.find((msg: any) => msg.meta?.isImplementationCard && msg.type === 'card')
  }, [messages])

  if (!implementationCard) {
    return null
  }

  return (
    <motion.div
      initial={ { opacity: 0, y: 20 } }
      animate={ { opacity: 1, y: 0 } }
      transition={ { duration: 0.5 } }
      className={ cn('mt-4', className) }
    >
      <MessageItem
        message={ implementationCard }
        onDelete={ () => {} }
        className="w-full"
        stateStore={ stateStore }
      />
    </motion.div>
  )
})

ImplementationCardRenderer.displayName = 'ImplementationCardRenderer'

/** Planning Scheme专用的ThinkingStream组件 */
const PlanningThinkingStream = memo<{
  content: string
  isActive: boolean
}>(({ content, isActive }) => {
  const [thinkingExpanded, setThinkingExpanded] = useState(true)
  const [displayedText, setDisplayedText] = useState('')
  const [isTyping, setIsTyping] = useState(false)

  useEffect(() => {
    if (!content || !isActive)
      return

    setIsTyping(true)
    setDisplayedText('')

    let currentIndex = 0
    const typeInterval = setInterval(() => {
      if (currentIndex < content.length) {
        setDisplayedText(content.slice(0, currentIndex + 1))
        currentIndex++
      }
      else {
        setIsTyping(false)
        clearInterval(typeInterval)
      }
    }, 20)

    return () => clearInterval(typeInterval)
  }, [content, isActive])

  if (!isActive || !content)
    return null

  return (
    <motion.div
      initial={ { opacity: 0, y: 10 } }
      animate={ { opacity: 1, y: 0 } }
      className="border border-gray-200 rounded-lg bg-white p-4 shadow-sm"
    >
      <div
        className="flex cursor-pointer items-center justify-between"
        onClick={ () => setThinkingExpanded(!thinkingExpanded) }
      >
        <div className="flex items-center gap-2">
          <Sparkles className="h-4 w-4 text-blue-500" />
          <span className="text-sm text-gray-700 font-medium">Planning Analysis</span>
          {isTyping && <LoadingIcon className="h-3 w-3" />}
        </div>
        <ChevronDown
          className={ `h-4 w-4 text-gray-400 transition-transform ${
            thinkingExpanded
              ? 'rotate-180'
              : ''
          }` }
        />
      </div>

      <AnimateShow show={ thinkingExpanded }>
        <div className="mt-3 whitespace-pre-wrap text-sm text-gray-600">
          {displayedText}
          {isTyping && <span className="animate-pulse">|</span>}
        </div>
      </AnimateShow>
    </motion.div>
  )
})

PlanningThinkingStream.displayName = 'PlanningThinkingStream'

export const ChatPage = memo<ChatPageProps>((
  {
    style,
    className,
    taskStore,
    stateStore,
    messageStore,
    mdToCodePreview,
    resetDistributionStore: _resetDistributionStore,
    reportStore,
    stepState,

    // TopBar 相关 props
    showTopBar = false,
    topBarAgents = [],
    onTopBarAgentClick,
    topBarDropdownExpanded = false,
    onTopBarDropdownToggle,

    // ChatV2 流程相关 props
    onStartAIAnalysis,
  },
) => {
  const { formData, isReportOpen, chatV2FlowMode, userDescription, uploadedImage } = stateStore.use()
  taskStore.use() // 保持响应式连接

  const chatHistoryRef = useRef<{ scrollToBottom: () => void }>(null)
  const stateStoreRef = useRef(stateStore)
  const taskStoreRef = useRef(taskStore)

  /** 更新 refs */
  stateStoreRef.current = stateStore
  taskStoreRef.current = taskStore

  // ChatV2 流程状态管理
  const [isThinking, setIsThinking] = useState(false)
  const [showForm, setShowForm] = useState(false)
  const [showWorkflow, setShowWorkflow] = useState(false)
  const [thinkingExpanded, setThinkingExpanded] = useState(true)
  const [isAnalysisStarted, setIsAnalysisStarted] = useState(false) // 控制是否已开始分析
  const [isButtonLoading, setIsButtonLoading] = useState(false) // 控制Continue to Strategy按钮的loading状态
  const [isButtonDisabled, setIsButtonDisabled] = useState(false) // 控制Continue to Strategy按钮的disabled状态
  const [cardsCreated, setCardsCreated] = useState(false) // 防重复创建标志
  const [competitiveReportItemId, setCompetitiveReportItemId] = useState<string>('') // 存储竞争对手分析报告项ID
  const [thinkingData, setThinkingData] = useState<string>('') // 存储从insight_report获取的thinking数据
  const [isThinkingDataReady, setIsThinkingDataReady] = useState(false) // 控制按钮是否可用（基于thinking数据是否准备好）
  const [showStrategyCard, setShowStrategyCard] = useState(false) // 控制Strategy卡片的显示
  const [strategyReportItemId, setStrategyReportItemId] = useState<string>('') // 存储Strategy报告项ID
  const [isStrategyApproved, setIsStrategyApproved] = useState(false) // 控制Strategy是否已批准
  const [displayText, setDisplayText] = useState<string>('') // 存储要在文本显示组件中展示的内容
  const [secondaryDisplayText, setSecondaryDisplayText] = useState<string>('') // 存储第二个文本显示组件的内容
  const [planningThinkingData, setPlanningThinkingData] = useState<string>('') // 存储Planning Scheme的thinking数据
  const [showPlanningThinking, setShowPlanningThinking] = useState(false) // 控制Planning Scheme ThinkingStream的显示
  const [showImplementationCard, setShowImplementationCard] = useState(false) // 控制Implementation卡片的显示
  const [showAnalysisResults, setShowAnalysisResults] = useState(false) // 新增：控制是否显示分析结果
  const [thinkingMessages, setThinkingMessages] = useState<Array<{ id: string, text: string }>>([]) // 存储thinking消息
  const [hasThinkingCompleted, setHasThinkingCompleted] = useState(false) // 跟踪thinking是否已完成
  const [workflowSteps, setWorkflowSteps] = useState<WorkflowStep[]>([]) // 存储工作流步骤
  const [isLoadingSteps, setIsLoadingSteps] = useState(false) // 工作流步骤加载状态

  /** 表单状态管理 */
  const [formDataLocal, setFormDataLocal] = useState<Partial<MarketStep1Params>>({
    brand: '',
    product_name: '',
    industry: '',
    competitor: '',
    product: '', // 独立的表单字段，不使用 userDescription
    pic: '', // 独立的表单字段，不使用 uploadedImage
    industry_id: 83, // 默认值
    ip: '', // 默认值
    role: '', // 默认值
  })
  const [formErrors, setFormErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  /** 监听ButtonStateManager的状态变化，同步按钮状态 */
  useEffect(() => {
    const handleButtonStateChange = (event: CustomEvent) => {
      const { loading, disabled } = event.detail
      setIsButtonLoading(loading)
      setIsButtonDisabled(disabled)
    }

    window.addEventListener('chatWorkflowButtonStateChange', handleButtonStateChange as EventListener)

    return () => {
      window.removeEventListener('chatWorkflowButtonStateChange', handleButtonStateChange as EventListener)
    }
  }, [])

  /** 处理 thinking 渲染完成 */
  const handleThinkingComplete = useCallback(() => {
    /** 避免重复触发 */
    if (hasThinkingCompleted) {
      console.log('⚠️ Thinking 已经完成，避免重复触发')
      return
    }

    console.log('✅ Thinking 内容渲染完成')
    setHasThinkingCompleted(true)

    /** 关闭 loading 状态 */
    if (isThinking) {
      setIsThinking(false)
      console.log('🔄 已关闭 loading 状态')
    }

    /** 收起 thinking 内容 */
    setThinkingExpanded(false)
    console.log('📦 已收起 thinking 内容')

    /** 短暂延迟后显示表单 */
    setTimeout(() => {
      /** 确保数据已经回填 */
      // @ts-ignore
      if (stateStore.collectedFormData) {
        console.log('📝 确认表单数据已准备好:', stateStore.collectedFormData)
      }

      setShowForm(true)
      stateStore.chatV2FlowMode = 'form'
      console.log('📋 表单已显示')

      /** 清除收集的数据，避免重复回填 */
      setTimeout(() => {
        // @ts-ignore
        if (stateStore.collectedFormData) {
          console.log('🧹 清除已使用的表单数据')
          // @ts-ignore
          stateStore.collectedFormData = null
        }
      }, 1000) // 表单显示1秒后清除数据
    }, 1000) // 1秒延迟，让用户看到渲染完成
  }, [isThinking, stateStore, hasThinkingCompleted])

  /** 监听 store 中的 thinking 内容并更新 */
  useEffect(() => {
    // @ts-ignore
    const thinkingData = stateStore?.thinkingContent || thinkingContent

    console.log('🔍 ChatPage thinking数据检查:', {
      hasThinkingContent: !!thinkingContent,
      hasStoreThinkingContent: !!stateStore?.thinkingContent,
      thinkingDataLength: thinkingData
        ? thinkingData.length
        : 0,
      isThinking,
    })

    if (thinkingData) {
      console.log('📝 ChatPage 接收到 thinking 内容:', thinkingData.substring(0, 100))
      /** 将 thinking 内容设置为消息 */
      setThinkingMessages([{
        id: 'thinking-content',
        text: thinkingData,
      }])

      /** 不再立即解除 loading，等待渲染完成回调 */
      if (chatV2FlowMode === 'thinking') {
        console.log('⏳ 等待 thinking 内容渲染完成...')
      }
    }
    // @ts-ignore
    else if (stateStore?.sseStreamMessages) {
      /** 如果没有 thinking 内容，使用 SSE 消息作为备份 */
      const sseMessages = stateStore.sseStreamMessages
      console.log('📨 使用SSE消息作为备份:', sseMessages?.length, '条')
      if (sseMessages && Array.isArray(sseMessages) && sseMessages.length > 0) {
        const messages = sseMessages.map((msg, index) => ({
          id: `msg-${index}`,
          text: msg.content || msg.text || 'Processing...',
        }))
        setThinkingMessages(messages)
      }
    }
    else if (chatV2FlowMode === 'thinking') {
      console.log('⏳ Thinking模式但没有数据，保持loading...')
      /** 保持loading状态 */
    }
  }, [stateStore, thinkingContent, isThinking, chatV2FlowMode])

  /** 监听 store 中的收集数据并更新表单 */
  useEffect(() => {
    if (collectedFormData && typeof collectedFormData === 'object') {
      console.log('📋 ChatPage 接收到收集的数据，准备回填:', collectedFormData)

      /** 直接更新表单数据 */
      setFormDataLocal((prev) => {
        const updated = {
          ...prev,
          brand: collectedFormData.brand_name || prev.brand,
          product_name: collectedFormData.product_name || prev.product_name,
          industry: collectedFormData.industry_name || prev.industry,
          competitor: collectedFormData.competitor_name || prev.competitor,
          /** 如果需要，可以添加其他字段的映射 */
        }
        console.log('📝 更新后的表单数据:', updated)
        return updated
      })

      console.log('✅ Action plan 表单数据已回填')

      /** 不要立即清除数据，等待表单显示后再清除 */
      /** 移除了 setTimeout 清除数据的逻辑 */
    }
  }, [collectedFormData, stateStore])

  /** 监听 userDescription 和 uploadedImage 并更新到表单的 product 和 pic 字段 */
  useEffect(() => {
    if (userDescription || uploadedImage) {
      console.log('📝 接收到用户输入，准备回填到表单:', { userDescription, uploadedImage })

      setFormDataLocal((prev) => {
        const updated = {
          ...prev,
          product: userDescription || prev.product, // 回填到 Product description 字段
          pic: uploadedImage || prev.pic, // 回填到 Upload 字段
        }
        console.log('✅ Product description 和 Upload 字段已回填:', updated)
        return updated
      })
    }
  }, [userDescription, uploadedImage])

  /** 处理 ChatV2 流程状态变化 */
  useEffect(() => {
    if (chatV2FlowMode === 'thinking') {
      /** 启动 Thinking 流程 */
      setIsThinking(true)
      setShowForm(false) // 确保表单隐藏
      setHasThinkingCompleted(false) // 重置完成状态

      /** 检查是否已经有thinking数据 */
      // @ts-ignore
      if (thinkingContent || stateStore?.thinkingContent) {
        console.log('✅ 已有thinking数据，等待渲染完成')
        /** 不要在这里设置任何状态，让 onThinkingComplete 处理 */
      }
      else {
        console.log('⏳ 等待thinking数据...')
        /**
         * 如果没有thinking数据，保持loading状态
         * 等待下一次effect触发（当thinking数据到达时）
         */
      }
    }
    else if (chatV2FlowMode === 'form') {
      /** 不要在这里直接显示表单，等待 thinking 渲染完成 */
      console.log('📋 Form 模式激活，但表单显示由 thinking 完成控制')
    }
    else if (chatV2FlowMode === 'workflow') {
      setShowForm(false)
      setShowWorkflow(true)
    }
  }, [chatV2FlowMode, stateStore, thinkingContent])

  /** 单独处理 text_link_disassemble 工作流 */
  useEffect(() => {
    if (chatV2FlowMode === 'text_link_disassemble') {
      /** 处理 text_link_disassemble 工作流模式 */
      console.log('🔄 ChatV2 flow mode is text_link_disassemble, starting workflow...')
      console.log('📋 Store data:', {
        taskInstanceId: stateStore.taskInstanceId,
        userDescription: stateStore.userDescription,
        detectedIntent: stateStore.detectedIntent,
      })
      setIsThinking(true)
      setThinkingExpanded(true) // 展开 thinking 内容
      setShowForm(false)
      setIsLoadingSteps(false)

      /** 直接执行 text_link_disassemble 工作流 */
      handleTextLinkDisassembleWorkflow()
    }
  }, [chatV2FlowMode]) // 只依赖于 chatV2FlowMode

  /** 当分析开始时，自动滚动到底部 */
  useEffect(() => {
    if (isAnalysisStarted) {
      /** 使用 requestAnimationFrame 确保 DOM 已更新 */
      requestAnimationFrame(() => {
        /** 第一次滚动：确保容器可见 */
        const container = document.querySelector('.ChatPageContainer .overflow-auto')
        if (container) {
          container.scrollTo({
            top: container.scrollHeight,
            behavior: 'smooth',
          })
        }

        /** 延迟后再次滚动：确保内容加载完成 */
        setTimeout(() => {
          if (container) {
            container.scrollTo({
              top: container.scrollHeight,
              behavior: 'smooth',
            })
          }
          /** ChatHistory 内部滚动 */
          chatHistoryRef.current?.scrollToBottom?.()
        }, 300)
      })
    }
  }, [isAnalysisStarted])

  /** 表单处理函数 */
  const handleFormChange = (field: keyof MarketStep1Params, value: string) => {
    setFormDataLocal(prev => ({ ...prev, [field]: value }))
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  /** 处理 text_link_disassemble 工作流 */
  const handleTextLinkDisassembleWorkflow = async () => {
    console.log('🚀 handleTextLinkDisassembleWorkflow called!')
    console.log('📊 Current store state:', {
      taskInstanceId: stateStore.taskInstanceId,
      userDescription: stateStore.userDescription,
      detectedIntent: stateStore.detectedIntent,
    })
    console.log('📊 LocalStorage state:', {
      taskInstanceId: localStorage.getItem('taskInstanceId'),
      userMessage: localStorage.getItem('userMessage'),
      detectedIntent: localStorage.getItem('detectedIntent'),
    })

    try {
      const taskInstanceId = stateStore.taskInstanceId || localStorage.getItem('taskInstanceId')
      const userMessage = stateStore.userDescription || localStorage.getItem('userMessage')

      if (!taskInstanceId || !userMessage) {
        console.error('❌ Missing required data for text_link_disassemble workflow:', {
          taskInstanceId,
          userMessage,
        })
        message.error('Missing required data. Please try again.')
        setIsThinking(false)
        return
      }

      console.log('✅ Workflow parameters ready:', { taskInstanceId, userMessage })

      /** 保存 taskInstanceId 到 localStorage，供后续 confirm-form 使用 */
      localStorage.setItem('text_link_taskInstanceId', taskInstanceId)
      console.log('💾 Saved text_link_taskInstanceId to localStorage:', taskInstanceId)

      /** 准备 SSE 请求参数 */
      const sseParams = {
        taskInstanceId,
        platform: 'rednote',
        workflowName: 'text_link_disassemble',
        parameters: {
          user_submit: userMessage,
        },
      }

      console.log('📤 Sending SSE request with params:', sseParams)

      /** 调用 SSE 流式接口 */
      const { userStore } = await import('@/store/userStore')
      const token = userStore.token
      console.log('🔑 Using token:', token
        ? 'Token exists'
        : 'No token!')

      const sseResponse = await fetch(`${import.meta.env.VITE_API_BASE_URL}/app/market/stream/execute-main`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(sseParams),
      })

      console.log('📥 SSE Response status:', sseResponse.status, sseResponse.statusText)

      if (!sseResponse.ok) {
        throw new Error(`SSE request failed: ${sseResponse.status} ${sseResponse.statusText}`)
      }

      /** 处理 SSE 流式数据 */
      const reader = sseResponse.body?.getReader()
      const decoder = new TextDecoder()

      console.log('📖 Starting to read SSE stream...')

      if (reader) {
        let buffer = ''
        let thinkingContent = ''
        let formData: any = {}
        let chunkCount = 0

        while (true) {
          const { done, value } = await reader.read()
          if (done) {
            console.log('✅ Stream reading completed, total chunks:', chunkCount)
            break
          }

          const chunk = decoder.decode(value, { stream: true })
          chunkCount++
          console.log(`📦 Chunk #${chunkCount} received, length: ${chunk.length}`)

          buffer += chunk
          const lines = buffer.split('\n')
          buffer = lines.pop() || ''

          for (const line of lines) {
            if (line.trim() === '')
              continue

            if (line.startsWith('data:')) {
              const data = line.slice(5).trim()

              if (data === '[DONE]') {
                console.log('✅ text_link_disassemble workflow completed')
                break
              }

              try {
                const jsonData = JSON.parse(data)

                /** 收集 thinking 内容并实时更新 */
                if (jsonData.node_title === 'thinking' && jsonData.content) {
                  thinkingContent += jsonData.content
                  console.log('💭 Thinking content received:', jsonData.content.substring(0, 100))

                  /** 实时更新 thinking 消息，触发流式渲染 */
                  setThinkingMessages([{
                    id: 'thinking-content',
                    text: thinkingContent,
                  }])

                  /** 确保 isThinking 状态为 true */
                  setIsThinking(true)
                  setShowForm(false)
                }

                /** 收集表单数据 */
                if (jsonData.node_title === 'form_collection' && jsonData.content) {
                  try {
                    const parsedForm = JSON.parse(jsonData.content)
                    formData = { ...formData, ...parsedForm }
                    console.log('📋 收集到表单数据:', formData)
                  }
                  catch (e) {
                    console.log('表单数据解析失败:', e)
                  }
                }
              }
              catch (e) {
                console.error('Failed to parse SSE data:', e)
              }
            }
          }
        }

        /** 工作流完成后，等待 thinking 渲染完成 */
        console.log('✅ text_link_disassemble 流完成，thinking 内容长度:', thinkingContent.length)

        /** 更新表单数据 */
        if (Object.keys(formData).length > 0) {
          setFormDataLocal(prev => ({
            ...prev,
            ...formData,
          }))
        }

        /** 不要立即关闭 thinking，让 TypewriterTextWithScroll 组件完成渲染 */
        // onThinkingComplete 回调会在渲染完成后自动调用
      }
    }
    catch (error) {
      console.error('text_link_disassemble workflow failed:', error)
      message.error('Workflow failed. Please try again.')
      setIsThinking(false)
    }
  }

  const handleFormSubmit = async () => {
    if (isSubmitting)
      return

    setFormErrors({})
    const errors: Record<string, string> = {}

    /** 验证所有必填字段 */
    const requiredFields = [
      { key: 'brand', label: 'Please enter the brand name to continue.' },
      { key: 'product_name', label: 'Please enter the product name to continue.' },
      { key: 'industry', label: 'Please enter the industry to continue.' },
      { key: 'competitor', label: 'Please enter the competitor information to continue.' },
      { key: 'product', label: 'Please enter the product description to continue.' },
      { key: 'pic', label: 'Please upload an image to continue.' },
    ]

    requiredFields.forEach(({ key, label }) => {
      const value = formDataLocal[key as keyof typeof formDataLocal]
      if (!value || (typeof value === 'string' && !value.trim())) {
        errors[key] = label
      }
    })

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors)
      message.error('Please fill in all required fields')
      return
    }

    setIsSubmitting(true)

    /** 提交表单时关闭 thinking loading */
    setIsThinking(false)

    try {
      /** 获取 taskInstanceId - 优先从 text_link_disassemble 响应中获取，否则使用初始的 */
      const textLinkTaskId = localStorage.getItem('text_link_taskInstanceId')
      const initialTaskId = localStorage.getItem('taskInstanceId')
      const taskInstanceId = textLinkTaskId || initialTaskId || ''

      if (!taskInstanceId) {
        message.error('Task instance ID not found. Please restart the process.')
        setIsSubmitting(false)
        return
      }

      console.log('📌 Using taskInstanceId:', taskInstanceId, {
        fromTextLink: !!textLinkTaskId,
        fromInitial: !!initialTaskId,
      })

      /** 准备接口请求参数 */
      const requestPayload = {
        taskInstanceId,
        formData: {
          brand_name: formDataLocal.brand || '',
          product_name: formDataLocal.product_name || '',
          industry_name: formDataLocal.industry || '',
          competitor_name: formDataLocal.competitor || '',
          inputText: formDataLocal.product || '',
          pic: formDataLocal.pic || '',
        },
      }

      console.log('📤 Submitting form to /api/app/market/confirm-form:', requestPayload)

      /** 调用确认表单接口 - request.post 已经返回解析后的数据 */
      const response = await request.post('/app/market/confirm-form', requestPayload)
      console.log('📨 Confirm form response:', response)

      /** 检查响应状态 - 注意：response 已经是 data 对象 */
      /** 如果 request 工具已经处理了错误，这里只需要检查业务逻辑 */
      if (!response) {
        throw new Error('No response from server')
      }

      console.log('✅ Form submitted successfully:', response)

      /** 如果返回了新的 taskInstanceId，使用新的 */
      let finalTaskInstanceId = taskInstanceId
      /** 检查两种可能的响应格式 */
      const newTaskId = response.taskInstanceId || response.data?.taskInstanceId
      if (newTaskId) {
        console.log('📌 Got new taskInstanceId from confirm-form:', newTaskId)
        /** 更新 taskInstanceId 为新的值 */
        finalTaskInstanceId = newTaskId
        localStorage.setItem('confirmed_taskInstanceId', finalTaskInstanceId)
      }

      /** 准备完整的表单数据用于后续流程 */
      const completeFormData = {
        industry: formDataLocal.industry || '',
        industry_id: 83,
        product_name: formDataLocal.product_name || '',
        ip: '专研彩妆',
        brand: formDataLocal.brand || '',
        role: '中国时尚彩妆领导品牌',
        product: formDataLocal.product || '',
        pic: formDataLocal.pic || '',
        competitor: formDataLocal.competitor || '',
        company: formDataLocal.brand || '',
        marketing_strategy: '好用',
        product_market: '',
        competitor_info: '女性彩妆',
      }

      /** 保存到 stateStore 并切换到工作流模式 */
      stateStore.cacheFormData = completeFormData
      stateStore.chatV2FlowMode = 'workflow'
      setIsLoadingSteps(true) // 开始加载工作流步骤

      message.success('Form submitted successfully!')

      /** 保存表单数据供 data_report 使用 */
      const dataReportParams = {
        brand_name: formDataLocal.brand || '',
        competitor_name: formDataLocal.competitor || '',
        industry_id: '83', // 转为字符串格式
        industry_name: formDataLocal.industry || '',
        product_name: formDataLocal.product_name || '',
        platform: 'rednote',
      }
      localStorage.setItem('dataReportParams', JSON.stringify(dataReportParams))
      console.log('💾 Saved data_report params:', dataReportParams)

      /** 异步执行 data_report 工作流 */
      const executeDataReport = async () => {
        try {
          console.log('📊 Starting data_report workflow asynchronously...')
          const token = userStore.token

          const dataReportRequest = {
            taskInstanceId: finalTaskInstanceId,
            platform: 'rednote',
            workflowName: 'data_report',
            parameters: dataReportParams,
          }

          console.log('📤 Executing data_report with params:', dataReportRequest)

          const sseResponse = await fetch(`${import.meta.env.VITE_API_BASE_URL}/app/market/stream/execute-main`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`,
            },
            body: JSON.stringify(dataReportRequest),
          })

          if (!sseResponse.ok) {
            console.error('❌ Failed to execute data_report:', sseResponse.statusText)
            return
          }

          /** 处理 SSE 流响应，收集各个节点的数据 */
          const reader = sseResponse.body?.getReader()
          const decoder = new TextDecoder()

          /** 收集 data_report 响应数据的变量 */
          const collectedData: Record<string, string> = {
            daren_data: '',
            biji_data: '',
            brand_data: '',
            competitor_data: '',
            product_data: '',
            brand_report: '',
            industry_report: '',
            industry_data: '',
          }

          if (reader) {
            console.log('📨 Receiving data_report SSE stream...')
            let buffer = ''

            while (true) {
              const { done, value } = await reader.read()
              if (done) {
                console.log('✅ data_report workflow completed')
                break
              }

              const chunk = decoder.decode(value, { stream: true })
              buffer += chunk
              const lines = buffer.split('\n')
              buffer = lines.pop() || ''

              for (const line of lines) {
                if (line.trim() === '')
                  continue

                if (line.startsWith('data:')) {
                  const data = line.slice(5).trim()
                  if (data === '[DONE]') {
                    console.log('📍 data_report stream ended')
                    continue
                  }

                  try {
                    const jsonData = JSON.parse(data)
                    console.log('📝 data_report message:', jsonData)

                    /** 根据 node_title 收集对应的数据 */
                    const nodeTitle = jsonData.node_title
                    const content = jsonData.content || ''

                    /** 收集各个节点的数据 */
                    if (nodeTitle && collectedData.hasOwnProperty(nodeTitle)) {
                      collectedData[nodeTitle] += content
                      console.log(`📋 Collected ${nodeTitle}:`, content.substring(0, 100))
                    }
                  }
                  catch (e) {
                    console.error('Failed to parse data_report SSE data:', e)
                  }
                }
              }
            }

            /** data_report 完成后，执行 insight_report */
            console.log('📊 Data collected from data_report:', collectedData)
            await executeInsightReport(collectedData, finalTaskInstanceId, token)
          }
        }
        catch (error) {
          console.error('❌ data_report workflow failed:', error)
          /** 不阻塞主流程，data_report 失败不影响其他功能 */
        }
      }

      /** 执行 insight_report 工作流 */
      const executeInsightReport = async (
        dataReportData: Record<string, string>,
        taskInstanceId: string,
        authToken: string,
      ) => {
        try {
          console.log('🎯 Starting insight_report workflow...')

          /** 准备 insight_report 的参数 */
          const insightReportParams = {
            brand_name: formDataLocal.brand || '',
            industry_name: formDataLocal.industry || '',
            competitor_name: formDataLocal.competitor || '',
            product_name: formDataLocal.product_name || '',
            industry_data: dataReportData.industry_data || '',
            daren_data: dataReportData.daren_data || '',
            biji_data: dataReportData.biji_data || '',
            brand_data: dataReportData.brand_data || '',
            competitor_data: dataReportData.competitor_data || '',
            product_data: dataReportData.product_data || '',
            brand_report: dataReportData.brand_report || '',
            industry_report: dataReportData.industry_report || '',
            platform: 'rednote',
          }

          console.log('📤 Executing insight_report with params:', {
            ...insightReportParams,
            /** 截断长文本以便于日志查看 */
            industry_data: `${insightReportParams.industry_data.substring(0, 100)}...`,
            daren_data: `${insightReportParams.daren_data.substring(0, 100)}...`,
            biji_data: `${insightReportParams.biji_data.substring(0, 100)}...`,
          })

          const insightReportRequest = {
            taskInstanceId,
            platform: 'rednote',
            workflowName: 'insight_report',
            parameters: insightReportParams,
          }

          const sseResponse = await fetch(`${import.meta.env.VITE_API_BASE_URL}/app/market/stream/execute-main`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${authToken}`,
            },
            body: JSON.stringify(insightReportRequest),
          })

          if (!sseResponse.ok) {
            console.error('❌ Failed to execute insight_report:', sseResponse.statusText)
            return
          }

          /** 处理 insight_report SSE 流响应 */
          const reader = sseResponse.body?.getReader()
          const decoder = new TextDecoder()

          /** 收集 insight_report 响应数据 */
          const insightCollectedData: Record<string, string> = {
            insight_report: '',
            thinking: '',
          }

          if (reader) {
            console.log('📨 Receiving insight_report SSE stream...')
            let buffer = ''

            while (true) {
              const { done, value } = await reader.read()
              if (done) {
                console.log('✅ insight_report workflow completed')
                break
              }

              const chunk = decoder.decode(value, { stream: true })
              buffer += chunk
              const lines = buffer.split('\n')
              buffer = lines.pop() || ''

              for (const line of lines) {
                if (line.trim() === '')
                  continue

                if (line.startsWith('data:')) {
                  const data = line.slice(5).trim()
                  if (data === '[DONE]') {
                    console.log('📍 insight_report stream ended')
                    continue
                  }

                  try {
                    const jsonData = JSON.parse(data)
                    console.log('📝 insight_report message:', jsonData)

                    /** 根据 node_title 收集对应的数据 */
                    const nodeTitle = jsonData.node_title
                    const content = jsonData.content || ''

                    /** 收集 insight_report 和 thinking 节点的数据 */
                    if (nodeTitle && insightCollectedData.hasOwnProperty(nodeTitle)) {
                      insightCollectedData[nodeTitle] += content
                      console.log(`📋 Collected ${nodeTitle} from insight_report:`, content.substring(0, 100))
                    }
                  }
                  catch (e) {
                    console.error('Failed to parse insight_report SSE data:', e)
                  }
                }
              }
            }

            /** insight_report 完成后，执行 competitor_report */
            console.log('📊 Data collected from insight_report:', insightCollectedData)
            await executeCompetitorReport(dataReportData, insightCollectedData, taskInstanceId, authToken)
          }
        }
        catch (error) {
          console.error('❌ insight_report workflow failed:', error)
          /** insight_report 失败不影响其他功能 */
        }
      }

      /** 执行 competitor_report 工作流 */
      const executeCompetitorReport = async (
        dataReportData: Record<string, string>,
        insightData: Record<string, string>,
        taskInstanceId: string,
        authToken: string,
      ) => {
        try {
          console.log('🏆 Starting competitor_report workflow...')

          /** 准备 competitor_report 的参数 */
          const competitorReportParams = {
            brand_name: formDataLocal.brand || '',
            industry_name: formDataLocal.industry || '',
            competitor_name: formDataLocal.competitor || '',
            product_name: formDataLocal.product_name || '',
            industry_data: dataReportData.industry_data || '',
            daren_data: dataReportData.daren_data || '',
            biji_data: dataReportData.biji_data || '',
            brand_data: dataReportData.brand_data || '',
            competitor_data: dataReportData.competitor_data || '',
            product_data: dataReportData.product_data || '',
            brand_report: dataReportData.brand_report || '',
            industry_report: dataReportData.industry_report || '',
            insight_report: insightData.insight_report || '',
            thinking: insightData.thinking || '',
            platform: 'rednote',
          }

          console.log('📤 Executing competitor_report with params:', {
            brand_name: competitorReportParams.brand_name,
            industry_name: competitorReportParams.industry_name,
            competitor_name: competitorReportParams.competitor_name,
            product_name: competitorReportParams.product_name,
            platform: competitorReportParams.platform,
            /** 截断长文本以便于日志查看 */
            insight_report: `${competitorReportParams.insight_report.substring(0, 100)}...`,
            thinking: `${competitorReportParams.thinking.substring(0, 100)}...`,
          })

          const competitorReportRequest = {
            taskInstanceId,
            platform: 'rednote',
            workflowName: 'competitor_report',
            parameters: competitorReportParams,
          }

          const sseResponse = await fetch(`${import.meta.env.VITE_API_BASE_URL}/app/market/stream/execute-main`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${authToken}`,
            },
            body: JSON.stringify(competitorReportRequest),
          })

          if (!sseResponse.ok) {
            console.error('❌ Failed to execute competitor_report:', sseResponse.statusText)
            return
          }

          /** 处理 competitor_report SSE 流响应 */
          const reader = sseResponse.body?.getReader()
          const decoder = new TextDecoder()

          if (reader) {
            console.log('📨 Receiving competitor_report SSE stream...')
            let buffer = ''

            while (true) {
              const { done, value } = await reader.read()
              if (done) {
                console.log('✅ competitor_report workflow completed')
                break
              }

              const chunk = decoder.decode(value, { stream: true })
              buffer += chunk
              const lines = buffer.split('\n')
              buffer = lines.pop() || ''

              for (const line of lines) {
                if (line.trim() === '')
                  continue

                if (line.startsWith('data:')) {
                  const data = line.slice(5).trim()
                  if (data === '[DONE]') {
                    console.log('📍 competitor_report stream ended')
                    continue
                  }

                  try {
                    const jsonData = JSON.parse(data)
                    console.log('📝 competitor_report message:', jsonData)

                    /** 可以在这里处理 competitor_report 的响应数据 */
                    /** 例如：保存竞品分析报告、更新 UI 等 */
                  }
                  catch (e) {
                    console.error('Failed to parse competitor_report SSE data:', e)
                  }
                }
              }
            }

            console.log('🎉 All report workflows completed successfully!')
          }
        }
        catch (error) {
          console.error('❌ competitor_report workflow failed:', error)
          /** competitor_report 失败不影响其他功能 */
        }
      }

      /** 异步执行 data_report，不阻塞主流程 */
      executeDataReport().catch((err) => {
        console.error('data_report execution error:', err)
      })

      /** 只有在确认成功时才执行 command_pot */
      /** 根据你提供的响应格式，response 本身就是完整的响应对象 */
      if (response.confirmStatus === 'SUCCESS' || (response.data && response.data.confirmStatus === 'SUCCESS')) {
        console.log('🚀 Starting command_pot workflow after successful form confirmation...')

        /** 执行 command_pot 工作流 */
        const executeCommandPot = async () => {
          try {
            const token = userStore.token
            /** 从 localStorage 和 stateStore 获取最新的 detectedIntent */
            const latestIntent = stateStore.detectedIntent || localStorage.getItem('detectedIntent') || 'create_post'
            console.log('📌 Using intent_result for command_pot:', latestIntent, {
              fromStore: stateStore.detectedIntent,
              fromLocalStorage: localStorage.getItem('detectedIntent'),
            })

            const executeParams = {
              taskInstanceId: finalTaskInstanceId, // 使用新的或原有的 taskInstanceId
              platform: 'rednote',
              workflowName: 'command_pot',
              parameters: {
                user_submit: stateStore.userDescription || '',
                intent_result: latestIntent, // 使用最新的 intent 值（可能来自 dialogue 工作流的 code 节点）
              },
            }

            console.log('📤 Executing command_pot with params:', executeParams)

            const sseResponse = await fetch(`${import.meta.env.VITE_API_BASE_URL}/app/market/stream/execute-main`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`,
              },
              body: JSON.stringify(executeParams),
            })

            if (!sseResponse.ok) {
              throw new Error(`Failed to execute command_pot: ${sseResponse.statusText}`)
            }

            /** 处理 SSE 流响应 */
            const reader = sseResponse.body?.getReader()
            const decoder = new TextDecoder()

            if (reader) {
              console.log('📨 Receiving command_pot SSE stream...')
              let buffer = ''

              while (true) {
                const { done, value } = await reader.read()
                if (done) {
                  console.log('✅ command_pot workflow completed')
                  break
                }

                const chunk = decoder.decode(value, { stream: true })
                buffer += chunk
                const lines = buffer.split('\n')
                buffer = lines.pop() || ''

                for (const line of lines) {
                  if (line.trim() === '')
                    continue

                  if (line.startsWith('data:')) {
                    const data = line.slice(5).trim()
                    if (data === '[DONE]') {
                      console.log('📍 command_pot stream ended')
                      continue
                    }

                    try {
                      const jsonData = JSON.parse(data)
                      console.log('📝 command_pot message:', jsonData)

                      /** 解析步骤数据 */
                      if (jsonData.content && jsonData.node_title === 'four_steps') {
                        try {
                          const stepsArray = JSON.parse(jsonData.content)
                          if (Array.isArray(stepsArray) && stepsArray.length > 0) {
                            const steps = stepsArray[0] // 获取第一个对象
                            const parsedSteps: WorkflowStep[] = []

                            /** 解析每个步骤 */
                            Object.entries(steps).forEach(([key, value]) => {
                              const stepMatch = key.match(/step(\d+)/)
                              if (stepMatch && typeof value === 'string') {
                                const stepNumber = Number.parseInt(stepMatch[1])

                                /** 尝试两种格式的解析 */
                                let agent = ''
                                let description = ''

                                /** 格式1："第X步. 部门名称：描述" */
                                const chineseMatch = value.match(/第.*步\.\s*([^：:]+)[：:](.+)/)
                                /** 格式2："Department Name: Description"（英文格式） */
                                const englishMatch = value.match(/^([^:]+):\s*(.+)/)

                                if (chineseMatch) {
                                  agent = `${chineseMatch[1]} Agent`
                                  description = chineseMatch[2].trim()
                                }
                                else if (englishMatch) {
                                  agent = `${englishMatch[1].trim()} Agent`
                                  description = englishMatch[2].trim()
                                }
                                else {
                                  /** 如果都不匹配，使用整个value作为描述 */
                                  agent = `Step ${stepNumber} Agent`
                                  description = value
                                }

                                parsedSteps.push({
                                  step: stepNumber,
                                  agent,
                                  description,
                                  borderColor: stepNumber === 1
                                    ? '#DD9DFF'
                                    : '#36D3FF',
                                })
                              }
                            })

                            /** 按步骤号排序 */
                            parsedSteps.sort((a, b) => a.step - b.step)

                            console.log('✅ 解析出的工作流步骤:', parsedSteps)
                            setWorkflowSteps(parsedSteps)
                            setIsLoadingSteps(false)
                          }
                        }
                        catch (parseError) {
                          console.error('Failed to parse steps content:', parseError)
                        }
                      }
                    }
                    catch (e) {
                      console.error('Failed to parse SSE data:', e)
                    }
                  }
                }
              }
            }
          }
          catch (error) {
            console.error('Failed to execute command_pot:', error)
            message.error('Failed to start AI workflow. Please try again.')
          }
        }

        /** 异步执行 command_pot，不阻塞UI */
        executeCommandPot()
      }
      else {
        console.log('⚠️ Form confirmation not successful or code not 200, skipping command_pot workflow')
        message.warning('Form confirmed but workflow not started. Please check the response.')
      }
    }
    catch (error) {
      console.error('Form submission failed:', error)
      message.error('Failed to submit form. Please try again.')
    }
    finally {
      setIsSubmitting(false)
    }
  }

  /** 转换 MarketStep1Params 到 TrendStep1Params */
  const convertToTrendParams = (marketParams: MarketStep1Params): TrendStep1Params => {
    return {
      brand: marketParams.brand,
      product_name: marketParams.product_name,
      industry: marketParams.industry,
      industry_id: marketParams.industry_id,
      competitor: marketParams.competitor || '',
      product: marketParams.product,
      role: marketParams.role,
      company: marketParams.company || marketParams.brand,
      pic: marketParams.pic,
      ip: marketParams.ip,
      marketing_strategy: marketParams.marketing_strategy || '',
      product_market: marketParams.product_market || '',
      competitor_info: marketParams.competitor_info || '',
    }
  }

  /** 为两个卡片同时调用流式API更新报告内容 */
  const updateBothCardsReport = useCallback(async (insightReportItemId: string, competitorReportItemId: string) => {
    console.warn('[updateBothCardsReport] 开始为两个卡片同时调用流式API:', { insightReportItemId, competitorReportItemId })

    try {
      /** 导入流式API函数 */
      const { callCozeStreamAPI, callCompetitorCozeStreamAPI } = await import('../stores/cozeStreamApi')

      /** 并发调用两个流式API */
      const [insightResult, competitorResult] = await Promise.allSettled([
        /** 调用 insight_report 流式接口 */
        (async () => {
          let isFirstContent = true
          await callCozeStreamAPI(
            (data: ParsedStreamData) => {
              // console.log(data, 'ParsedStreamDataParsedStreamDataParsedStreamDataParsedStreamDataParsedStreamData')

              const { reportStore } = trendAg
              const targetItem = reportStore.items.find(item => item.id === insightReportItemId)

              if (data.type === 'title' && data.title && targetItem) {
                targetItem.title = data.title
              }
              else if (data.type === 'text' && data.content && targetItem) {
                if (isFirstContent) {
                  targetItem.content = data.content
                  isFirstContent = false
                }
                else {
                  targetItem.content += data.content
                }
              }
              else if (data.type === 'thinking' && data.content) {
                /** 保存thinking数据并立即更新stateStore */
                console.warn('[updateBothCardsReport] 📝 接收到thinking数据:', `${data.content.substring(0, 100)}...`)
                setThinkingData(data.content)
                setIsThinkingDataReady(true)

                /** 立即更新stateStore中的thinking内容，确保ThinkingStream组件能接收到数据 */
                stateStore.thinkingContent = data.content
                stateStore.isThinkingActive = true
                console.warn('[updateBothCardsReport] ✅ Thinking数据已更新到stateStore，ThinkingStream将显示实际内容')
              }
              else if (data.type === 'complete') {
                console.warn('[updateBothCardsReport] ✅ Insight Report 流式数据接收完成')
              }
            },
            (error: Error) => {
              console.error('[updateBothCardsReport] Insight Report 流式API调用失败:', error)
              const { reportStore } = trendAg
              const targetItem = reportStore.items.find(item => item.id === insightReportItemId)
              if (targetItem) {
                targetItem.content = `数据获取失败：${error.message}\n\n请检查网络连接后重试。`
              }
            },
            () => {
              console.warn('[updateBothCardsReport] Insight Report 流式响应完成')
            },
          )
        })(),

        /** 调用 competitor_report 流式接口 */
        (async () => {
          let isFirstContent = true
          await callCompetitorCozeStreamAPI(
            (data: ParsedStreamData) => {
              const { reportStore } = trendAg
              const targetItem = reportStore.items.find(item => item.id === competitorReportItemId)

              if (data.type === 'title' && data.title && targetItem) {
                targetItem.title = data.title
              }
              else if (data.type === 'text' && data.content && targetItem) {
                if (isFirstContent) {
                  targetItem.content = data.content
                  isFirstContent = false
                }
                else {
                  targetItem.content += data.content
                }
              }
              else if (data.type === 'complete') {
                console.warn('[updateBothCardsReport] ✅ Competitor Report 流式数据接收完成')
              }
            },
            (error: Error) => {
              console.error('[updateBothCardsReport] Competitor Report 流式API调用失败:', error)
              const { reportStore } = trendAg
              const targetItem = reportStore.items.find(item => item.id === competitorReportItemId)
              if (targetItem) {
                targetItem.content = `竞争对手分析失败：${error.message}\n\n请检查网络连接后重试。`
              }
            },
            () => {
              console.warn('[updateBothCardsReport] Competitor Report 流式响应完成')
            },
          )
        })(),
      ])

      /** 检查执行结果 */
      if (insightResult.status === 'rejected') {
        console.error('[updateBothCardsReport] Insight Report 执行失败:', insightResult.reason)
      }
      if (competitorResult.status === 'rejected') {
        console.error('[updateBothCardsReport] Competitor Report 执行失败:', competitorResult.reason)
      }

      console.warn('[updateBothCardsReport] 双流式API调用完成')
    }
    catch (error) {
      console.error('[updateBothCardsReport] 执行失败:', error)
    }
  }, [stateStore])

  /** 创建自定义的两个策略卡片 */
  const createCustomStrategyCards = useCallback(() => {
    /** 防重复创建检查 */
    if (cardsCreated) {
      console.warn('[createCustomStrategyCards] 卡片已存在，跳过创建')
      return
    }

    console.warn('[createCustomStrategyCards] 开始创建卡片，时间戳:', Date.now())

    const curStep = 'step0' as any
    const currentLeftIcon = 'researchAnalyst' // 使用 Research Analyst 图标

    /** 创建第一个报告项 - Interior Design Industry Research Report 2025 */
    const industryReportItem = addReportItem({
      type: 'markdown',
      title: 'Interior Design Industry Research Report 2025',
      content: 'Analyzing interior design industry trends and market dynamics...',
      meta: {
        step: curStep,
        canTransformCode: true,
      },
    }, true, true, trendAg)

    /** 创建第二个报告项 - Competitive Analysis */
    const competitiveReportItem = addReportItem({
      type: 'markdown',
      title: 'Competitive Analysis',
      content: 'Analyzing competitor strategies and positioning opportunities...',
      meta: {
        step: curStep,
        canTransformCode: true,
      },
    }, true, false, trendAg)

    /** 保存竞争对手分析报告项ID */
    setCompetitiveReportItemId(competitiveReportItem.id)
    console.warn('[createCustomStrategyCards] 设置竞争对手分析报告项ID:', competitiveReportItem.id)

    /** 手动设置基础时间戳，确保卡片显示在正确位置 */
    const baseTimestamp = Date.now() - 10000 // 减去10秒，确保卡片显示在其他消息之前

    /** 第一个卡片：Interior Design Industry Research Report 2025 */
    const firstCard = createCardMessage(
      {
        title: 'Interior Design Industry Research Report 2025',
        description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit...',
        variant: 'success',
        onClick: () => {
          stateStoreRef.current.isReportOpen = true
          taskStoreRef.current.currentStep = curStep
          eventBus.emit(DistributionEvent.SetActiveTab, industryReportItem.id)
        },
        cardConfig: {
          leftIcon: {
            show: true,
            icon: currentLeftIcon,
            size: 'lg' as const,
          },
          rightIcon: {
            show: true,
            icon: 'card-right-icon1',
            size: 'md' as const,
          },
          content: {
            title: 'Interior Design Industry Research Report 2025',
            description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit...',
          },
          layout: 'simple' as const,
        },
      },
      {
        meta: {
          step: curStep,
          cardId: 'interior-design-report',
        },
      },
      trendAg,
    )

    /** 第二个卡片：Competitive Analysis */
    const secondCard = createCardMessage(
      {
        title: 'Competitive Analysis',
        description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit...',
        variant: 'success',
        onClick: () => {
          stateStoreRef.current.isReportOpen = true
          taskStoreRef.current.currentStep = curStep
          eventBus.emit(DistributionEvent.SetActiveTab, competitiveReportItem.id)
        },
        cardConfig: {
          leftIcon: {
            show: true,
            icon: currentLeftIcon,
            size: 'lg' as const,
          },
          rightIcon: {
            show: true,
            icon: 'card-right-icon2',
            size: 'md' as const,
          },
          content: {
            title: 'Competitive Analysis',
            description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit...',
          },
          layout: 'simple' as const,
        },
      },
      {
        meta: {
          step: curStep,
          cardId: 'competitive-analysis',
        },
      },
      trendAg,
    )

    /** 手动调整时间戳，确保卡片按正确顺序显示在其他消息之前 */
    firstCard.timestamp = baseTimestamp + 1 // 第一个卡片
    secondCard.timestamp = baseTimestamp + 2 // 第二个卡片

    console.warn('[createCustomStrategyCards] 卡片已创建，时间戳:', {
      firstCard: { id: firstCard.id, timestamp: firstCard.timestamp, title: firstCard.card?.title },
      secondCard: { id: secondCard.id, timestamp: secondCard.timestamp, title: secondCard.card?.title },
      totalMessages: trendAg.messageStore.messages.length,
      messageTypes: trendAg.messageStore.messages.map(msg => ({ id: msg.id, type: msg.type, timestamp: msg.timestamp })),
    })

    /** 为两个卡片同时调用流式API更新报告内容 */
    setTimeout(() => {
      updateBothCardsReport(industryReportItem.id, competitiveReportItem.id)
    }, 200) // 短暂延迟确保卡片创建完成

    /** 自动打开报告面板并显示第一个卡片的tab */
    setTimeout(() => {
      stateStoreRef.current.isReportOpen = true
      eventBus.emit(DistributionEvent.SetActiveTab, industryReportItem.id)
      console.warn('[createCustomStrategyCards] 自动打开报告面板，显示第一个卡片tab')
    }, 300) // 稍后执行，确保卡片和报告项都已创建

    /** 设置创建完成标志，防止重复创建 */
    setCardsCreated(true)
  }, [cardsCreated, updateBothCardsReport]) // 恢复 updateBothCardsReport 依赖

  /** 独立的Strategy流式API更新函数 - 避免与现有双流式调用冲突 */
  const updateStrategyReport = useCallback(async (strategyReportItemId: string) => {
    console.warn('[updateStrategyReport] 开始Strategy独立流式API调用:', { strategyReportItemId })

    try {
      /** 导入Strategy专用的流式API函数 */
      const { callStrategyCozeStreamAPI } = await import('../stores/cozeStreamApi')

      /** 调用Strategy流式接口 */
      await callStrategyCozeStreamAPI(
        (data) => {
          /** 处理Strategy流式数据 */
          if (data.type === 'text' && data.content) {
            const { reportStore } = trendAg
            const targetItem = reportStore.items.find(item => item.id === strategyReportItemId)
            if (targetItem) {
              if (data.content.startsWith('# ') || data.content.startsWith('## ')) {
                targetItem.content = data.content
              }
              else {
                targetItem.content += data.content
              }
            }
          }
          else if (data.type === 'complete') {
            console.warn('[updateStrategyReport] ✅ Strategy流式数据接收完成')
          }
        },
        (error: Error) => {
          console.error('[updateStrategyReport] Strategy流式API调用失败:', error)
          const { reportStore } = trendAg
          const targetItem = reportStore.items.find(item => item.id === strategyReportItemId)
          if (targetItem) {
            targetItem.content = `Strategy Implementation Plan 生成失败：${error.message}\n\n请检查网络连接后重试。`
          }
        },
        () => {
          console.warn('[updateStrategyReport] Strategy流式响应完成')
        },
      )

      console.warn('[updateStrategyReport] Strategy独立流式API调用完成')
    }
    catch (error) {
      console.error('[updateStrategyReport] Strategy流式API执行失败:', error)
    }
  }, [])

  /** Planning Scheme流式API更新函数 - 创建专门的报告项 */
  const updatePlanningSchemeReport = useCallback(async () => {
    try {
      const { addReportItem } = await import('../stores/create')

      const planningSchemeReportItem = addReportItem({
        type: 'markdown',
        title: 'Planning Scheme Report',
        content: '正在生成营销策划方案...',
        meta: {
          step: 'step0',
          canTransformCode: true,
        },
      }, true, false, trendAg)

      /** 导入Planning Scheme专用的流式API函数 */
      const { callPlanningSchemeCozeStreamAPI } = await import('../stores/cozeStreamApi')

      /** 调用Planning Scheme流式接口 */
      await callPlanningSchemeCozeStreamAPI(
        (data) => {
          if (data.type === 'thinking' && data.content) {
            /** 处理Planning Scheme专用的thinking数据 */
            setPlanningThinkingData(data.content)
            setShowPlanningThinking(true)

            /** 同时更新原有的thinking数据以保持兼容性 */
            setThinkingData(data.content)
            setIsThinkingDataReady(true)
            stateStore.thinkingContent = data.content
            stateStore.isThinkingActive = true
          }
          else if (data.type === 'title' && data.title) {
            const { reportStore } = trendAg
            const planningItem = reportStore.items.find(item => item.id === planningSchemeReportItem.id)
            if (planningItem) {
              planningItem.title = data.title
            }
          }
          else if (data.type === 'text' && data.content) {
            const { reportStore } = trendAg
            const planningItem = reportStore.items.find(item => item.id === planningSchemeReportItem.id)
            if (planningItem) {
              if (data.content.startsWith('# ') || data.content.startsWith('## ')) {
                planningItem.content = data.content
              }
              else {
                planningItem.content += data.content
              }
            }
          }
        },
        (error: Error) => {
          console.error('[updatePlanningSchemeReport] Planning Scheme流式API调用失败:', error)
          const { reportStore } = trendAg
          const planningItem = reportStore.items.find(item => item.id === planningSchemeReportItem.id)

          if (planningItem) {
            planningItem.content = `Planning Scheme报告生成失败：${error.message}\n\n请检查网络连接后重试。`
          }
        },
        () => {
          /** 流式完成后，自动打开报告面板并更新卡片点击事件 */
          setTimeout(() => {
            stateStore.isReportOpen = true
            eventBus.emit(DistributionEvent.SetActiveTab, planningSchemeReportItem.id)

            /** 更新Planning Scheme卡片的点击事件 */
            const planningSchemeCard = trendAg.messageStore.messages.find(
              msg => msg.meta?.isPlanningSchemeCard === true,
            )
            if (planningSchemeCard && planningSchemeCard.card) {
              planningSchemeCard.card.onClick = () => {
                stateStore.isReportOpen = true
                eventBus.emit(DistributionEvent.SetActiveTab, planningSchemeReportItem.id)
              }
            }
          }, 500)
        },
      )
    }
    catch (error) {
      console.error('Planning Scheme API failed:', error)
    }
  }, [stateStore])

  /** 创建Planning Scheme后续卡片 - 在Approve Strategy后显示 */
  const createPlanningFollowUpCard = useCallback(() => {
    const curStep = 'step1' as any
    const currentLeftIcon = 'researchAnalyst'

    const followUpCard = createCardMessage(
      {
        title: 'Implementation Plan',
        description: '正在生成详细的实施计划，包含时间线、资源配置和执行步骤...',
        variant: 'success',
        onClick: () => {
          console.warn('[ImplementationCard] 点击实施计划卡片')
        },
        cardConfig: {
          leftIcon: {
            show: true,
            icon: currentLeftIcon,
            size: 'lg' as const,
          },
          rightIcon: {
            show: true,
            icon: 'card-right-icon1',
            size: 'md' as const,
          },
          content: {
            title: 'Implementation Plan',
            description: '实施计划生成中，包含详细的执行步骤、时间安排和资源分配。',
          },
          layout: 'simple' as const,
          button: {
            text: 'View Details',
            variant: 'gradient-border' as const,
            position: 'bottom-left' as const,
            disabled: false,
            onClick: () => {
              console.warn('[Implementation Button] 查看实施详情按钮被点击')
            },
          },
        },
      },
      {
        meta: {
          step: curStep,
          cardId: 'implementation-plan-card',
          isImplementationCard: true,
        },
      },
      trendAg,
    )

    /** 设置时间戳确保显示在正确位置 */
    const futureTimestamp = Date.now() + 120000 // 比Planning Scheme卡片晚60秒
    followUpCard.timestamp = futureTimestamp

    return { followUpCard }
  }, [])

  /** 创建Planning Scheme独立卡片 - 创建独立的DOM节点和卡片 */
  const createPlanningSchemeCard = useCallback(() => {
    const curStep = 'step0' as any
    const currentLeftIcon = 'researchAnalyst' // 使用 Research Analyst 图标

    /** 创建Planning Scheme专用的卡片消息，标记为独立的Strategy卡片 */
    const planningSchemeCard = createCardMessage(
      {
        title: 'Planning Scheme Report',
        description: '正在生成营销策划方案，包含完整的实施路线图和关键指标...',
        variant: 'success',
        onClick: () => {
          const planningReport = trendAg.reportStore.items.find(item => item.title === 'Planning Scheme Report')
          if (planningReport) {
            stateStore.isReportOpen = true
            eventBus.emit(DistributionEvent.SetActiveTab, planningReport.id)
          }
        },
        cardConfig: {
          leftIcon: {
            show: true,
            icon: currentLeftIcon,
            size: 'lg' as const,
          },
          rightIcon: {
            show: true,
            icon: 'card-right-icon1',
            size: 'md' as const,
          },
          content: {
            title: 'Planning Scheme Report',
            description: '营销策划方案生成中，包含实施路线图、关键指标和预算分配等详细内容。',
          },
          layout: 'simple' as const,
          button: {
            text: isStrategyApproved
              ? 'Approved'
              : 'Approve Strategy',
            variant: isStrategyApproved
              ? 'success' as const
              : 'gradient-border' as const,
            position: 'bottom-left' as const,
            disabled: isStrategyApproved,
            onClick: () => {
              if (isStrategyApproved)
                return

              setIsStrategyApproved(true)
              stateStoreRef.current.isReportOpen = true
              taskStoreRef.current.currentStep = 'step0' as any
              setSecondaryDisplayText(`Approve Strategy`)

              /** 创建Planning Scheme后续卡片并显示 */
              setTimeout(() => {
                createPlanningFollowUpCard()
                setShowImplementationCard(true)
              }, 1000)
            },
          },
        },
      },
      {
        meta: {
          step: curStep,
          cardId: 'planning-scheme-report',
          isStrategyCard: true, // 标记为Strategy卡片，让StrategyCardRenderer能找到它
          isPlanningSchemeCard: true, // 额外标记为Planning Scheme卡片
        },
      },
      trendAg,
    )

    /** 设置特殊的时间戳，确保卡片显示在ThinkingStream下方 */
    const futureTimestamp = Date.now() + 60000
    planningSchemeCard.timestamp = futureTimestamp

    return { planningSchemeCard }
  }, [stateStore, isStrategyApproved, createPlanningFollowUpCard])

  /** 处理开始分析按钮点击 */
  const handleStartAnalysis = async () => {
    /** 临时移除thinking数据依赖，提升用户体验 */
    if (isButtonLoading || isButtonDisabled)
      return

    /** 标记分析已开始，显示分析结果区域 */
    setIsAnalysisStarted(true)
    setIsButtonLoading(true)

    /** 激活ThinkingStream，优先使用从API接收到的thinking数据 */
    stateStore.isThinkingActive = true

    /** 设置ThinkingStream内容 */
    if (thinkingData && thinkingData.trim() && !thinkingData.includes('正在分析市场趋势')) {
      stateStore.thinkingContent = thinkingData
    }
    else if (stateStore.thinkingContent && stateStore.thinkingContent.trim() && !stateStore.thinkingContent.includes('正在思考') && !stateStore.thinkingContent.includes('正在分析市场趋势')) {
      /** 保持现有内容 */
    }
    else {
      stateStore.thinkingContent = '正在思考...'
    }

    /** 创建Planning Scheme卡片 */
    setTimeout(() => {
      createPlanningSchemeCard()
    }, 300)

    setShowStrategyCard(true)
    setDisplayText(`Continue to Strategy`)

    /** 开始分析后持续监听消息变化并滚动 */
    const scrollInterval = setInterval(() => {
      const container = document.querySelector('.ChatPageContainer .overflow-auto')
      if (container) {
        const isNearBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 100
        /** 只有在接近底部时才自动滚动，避免干扰用户手动滚动 */
        if (isNearBottom) {
          container.scrollTo({
            top: container.scrollHeight,
            behavior: 'smooth',
          })
        }
      }
    }, 500)

    /** 启动Planning Scheme流式API调用 */
    try {
      await updatePlanningSchemeReport()
      await new Promise(resolve => setTimeout(resolve, 2000))

      setTimeout(() => clearInterval(scrollInterval), 2000)
      setIsButtonDisabled(true)
    }
    catch (error) {
      console.error('Planning Scheme analysis failed:', error)
      setIsAnalysisStarted(false)
      clearInterval(scrollInterval)
    }
    finally {
      setIsButtonLoading(false)
    }
  }

  useBindWinEvent('resize', () => {
    stateStore.isReportOpen = false
    stateStore.isAgentCollapsed = true
  })

  /** 当页面加载且有表单数据时，创建一个开始按钮让用户手动启动 */
  useEffect(() => {
    if (!formData) {
      return
    }

    /** 创建一个初始任务，让用户点击开始 */
    if (taskStore.agentTasks.length === 0) {
      taskStore.agentTasks.push({
        id: 'start',
        title: 'Marketing Team Ready',
        description: '点击下方按钮开始执行营销方案生成',
        status: 'waiting',
        actions: [{ label: 'Continue to Strategy', type: 'primary' }],
        step: 'step0' as any,
      })
    }
  }, [formData, taskStore.agentTasks])

  /** 监听 showWorkflow 变化，在表单提交后立即创建策略卡片 */
  useEffect(() => {
    if (showWorkflow && !cardsCreated && !isAnalysisStarted) {
      /** 只有在表单提交后（非Continue to Strategy按钮点击后）才创建卡片 */
      console.warn('[useEffect] 表单提交后创建策略卡片')
      setTimeout(() => {
        /** 调用自定义的策略卡片创建方法 */
        createCustomStrategyCards()
      }, 100) // 短暂延迟确保状态更新完成
    }
    else if (!showWorkflow && cardsCreated) {
      /** 当 showWorkflow 变为 false 时，重置创建标志，允许下次重新创建 */
      console.warn('[useEffect] 重置卡片创建标志')
      setCardsCreated(false)
    }
    else if (showWorkflow && !cardsCreated && isAnalysisStarted) {
      /** Continue to Strategy按钮点击后，跳过卡片创建，因为会使用planning_scheme */
      console.warn('[useEffect] Continue to Strategy按钮点击后，跳过双流式API调用')
    }
  }, [showWorkflow, cardsCreated, isAnalysisStarted, createCustomStrategyCards])

  useInsertionEffect(() => {
    const overflow = document.body.style.overflow
    document.body.style.overflow = 'hidden'
    return () => {
      document.body.style.overflow = overflow
    }
  }, [])

  return <div
    className={ cn(
      'ChatPageContainer flex flex-row h-full overflow-hidden p-4 gap-4',
      className,
    ) }
    style={ style }
  >
    {/* 主内容区 */}
    <div className="flex flex-1 flex-col overflow-hidden rounded-2xl bg-white">
      {/* TopBar - 只在左侧聊天区域上方显示 */}
      {showTopBar && (
        <div className="flex-shrink-0 border-b border-gray-100">
          <TopBar
            agents={ topBarAgents }
            onAgentClick={ onTopBarAgentClick }
            dropdownExpanded={ topBarDropdownExpanded }
            onDropdownToggle={ onTopBarDropdownToggle }
            containerClassName="relative"
          />
        </div>
      )}

      {/* 聊天内容区域 */}
      <div className="flex-1 overflow-hidden">
        {chatV2FlowMode
          ? (
            // ChatV2 流程界面 - 使用可滚动容器
              <div className="relative h-full flex flex-col">
                <div className="flex-1 overflow-auto pb-28">
                  {' '}
                  {/* 为底部输入框预留空间 */}
                  <div className="mx-auto max-w-4xl">
                    <ChatWorkflow
                      showContentDisplay
                      content={ userDescription }
                      uploadedImage={ uploadedImage }
                      showThinking
                      isThinking={ isThinking }
                      thinkingExpanded={ thinkingExpanded }
                      onThinkingToggle={ setThinkingExpanded }
                      onThinkingComplete={ handleThinkingComplete }
                      thinkingMessages={ thinkingMessages.length > 0
                        ? thinkingMessages
                        : undefined }
                      showWorkflow={ showWorkflow }
                      workflowSteps={ workflowSteps }
                      isLoadingWorkflowSteps={ isLoadingSteps }
                      showForm={ showForm && !showWorkflow }
                      formData={ formDataLocal }
                      formErrors={ formErrors }
                      formUploadedImage={ formDataLocal.pic }
                      isSubmitting={ isSubmitting }
                      onFormChange={ handleFormChange }
                      onFormSubmit={ handleFormSubmit }
                      onImageUpload={ async (file) => {
                        try {
                          console.log('开始上传图片到 OSS...')

                          /** 先创建本地预览 URL */
                          const localPreviewUrl = URL.createObjectURL(file)
                          handleFormChange('pic', localPreviewUrl)

                          /** 上传到 OSS 获取 URL */
                          const uploadResult = await FileAPI.upFileToUrl([file])
                          if (uploadResult?.downloadLoadFileDetails?.[0]?.url) {
                            const ossUrl = uploadResult.downloadLoadFileDetails[0].url
                            console.log('图片上传成功，OSS URL:', ossUrl)

                            /** 清理本地预览 URL */
                            URL.revokeObjectURL(localPreviewUrl)

                            /** 更新为 OSS URL */
                            handleFormChange('pic', ossUrl)
                            message.success('图片上传成功')
                          }
                          else {
                            message.error('图片上传失败')
                            handleFormChange('pic', '')
                          }
                        }
                        catch (error) {
                          console.error('图片上传失败:', error)
                          message.error('图片上传失败，请重试')
                          handleFormChange('pic', '')
                        }
                      } }
                      onImageRemove={ () => handleFormChange('pic', '') }
                      workflowTitle="Your AI Agent Team‘s Action plan"
                      workflowDescription="Perfect! Your brand profile is ready. Now I'm bringing together my AI agent team to create amazing trend analysis for you. Here's how we'll work together:"
                      onStartAnalysis={ handleStartAnalysis }
                      showAskInput={ false } // 禁用内置的 Ask 输入框
                      askInputPlaceholder=""
                      askInputValue=""
                      askInputDisabled
                      onAskInputSubmit={ () => {} }
                    />

                    {/* 分析结果区域 - 表单提交后立即显示，包含策略卡片和Continue to Strategy按钮 */}
                    {showWorkflow && (
                      <motion.div
                        className="border-gray-200"
                        initial={ { opacity: 0, y: 20 } }
                        animate={ { opacity: 1, y: 0 } }
                        transition={ { duration: 0.5 } }
                      >

                        {/* 策略卡片显示区域 - 在motion组件最顶部 */}
                        <div className="mb-6">
                          <ChatHistory
                            taskStore={ taskStore }
                            messageStore={ messageStore }
                            ref={ chatHistoryRef }
                            className="min-h-0 w-full"
                            onDeleteMessage={ removeMessage }
                            stateStore={ stateStore }
                          />
                        </div>

                        {/* Continue to Strategy 按钮 - 在卡片下方 */}
                        <div className="mb-6 flex justify-start">
                          <button
                            className={ cn(
                              'px-8 py-3 rounded-full text-sm font-medium transition-colors bg-black text-white hover:bg-gray-800',
                              'disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-black',
                              isButtonLoading && 'cursor-wait',
                            ) }
                            disabled={ isButtonLoading || isButtonDisabled }
                            onClick={ handleStartAnalysis }
                          >
                            <div className="flex items-center gap-2">
                              {isButtonLoading && (
                                <div className="h-4 w-4 animate-spin border-2 border-white border-t-transparent rounded-full" />
                              )}
                              Continue to Strategy
                            </div>
                          </button>
                        </div>

                        {/* 文本显示组件 - 在按钮下方，ThinkingStream上方 */}
                        {displayText && (
                          <TextDisplayWithPagination
                            content={ displayText }
                            charsPerPage={ 600 }
                            onCopySuccess={ () => {
                              console.warn('[TextDisplay] 📋 文本复制成功')
                              /** 这里可以添加toast提示 */
                            } }
                            onCopyError={ (error) => {
                              console.error('[TextDisplay] 复制失败:', error)
                              /** 这里可以添加错误提示 */
                            } }
                          />
                        )}
                        {/* Thinking 流式显示组件 - 在文本显示组件下方 */}
                        {displayText && (
                          <ThinkingStream stateStore={ stateStore } />
                        )}

                        {/* Strategy Implementation Plan 卡片容器 - 在ThinkingStream下方 */}
                        {showStrategyCard && (
                          <StrategyCardRenderer
                            messageStore={ trendAg.messageStore }
                            stateStore={ stateStore }
                          />
                        )}

                        {/* 第二个文本显示组件 - 在Strategy卡片下方，显示实施路线图 */}
                        {secondaryDisplayText && isStrategyApproved && (
                          <TextDisplayWithPagination
                            content={ secondaryDisplayText }
                            charsPerPage={ 500 }
                            className="mt-4"
                            onCopySuccess={ () => {
                              console.warn('[SecondaryTextDisplay] 📋 实施路线图复制成功')
                              /** 这里可以添加toast提示 */
                            } }
                            onCopyError={ (error) => {
                              console.error('[SecondaryTextDisplay] 复制失败:', error)
                              /** 这里可以添加错误提示 */
                            } }
                          />
                        )}
                        {/* 第二个Thinking 流式显示组件 - 在Implementation卡片下方 */}
                        {secondaryDisplayText && isStrategyApproved && (
                          <ThinkingStream stateStore={ stateStore } />
                        )}
                        {/* Implementation Plan卡片容器 - 在Planning Scheme ThinkingStream下方 */}
                        {showImplementationCard && (
                          <ImplementationCardRenderer
                            messageStore={ trendAg.messageStore }
                            stateStore={ stateStore }
                            className="mt-4"
                          />
                        )}

                      </motion.div>
                    )}
                  </div>
                </div>

                {/* 悬浮的输入框 - 始终显示在底部 */}
                {showWorkflow && (
                  <div className="absolute bottom-0 left-0 right-0 bg-white p-4">
                    <div className="mx-auto max-w-4xl">
                      <div className="flex items-center gap-3 border rounded-lg bg-gray-50 p-3">
                        <input
                          type="text"
                          value={ isAnalysisStarted
                            ? 'AI analysis in progress...'
                            : 'Analysis will start from workflow above' }
                          readOnly
                          className="flex-1 border-none bg-transparent text-gray-700 outline-none placeholder-gray-400"
                          placeholder={ isAnalysisStarted
                            ? 'Analysis running...'
                            : 'Click the button in workflow to start analysis...' }
                        />
                        {isAnalysisStarted && (
                          <div className="h-8 w-8 flex items-center justify-center">
                            <div className="h-4 w-4 animate-spin border-b-2 border-blue-500 rounded-full"></div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )
          : (
            /** 正常的聊天界面 */
              <div className="relative h-full flex flex-col">
                <motion.div
                  layout
                  className={ cn(
                    'flex-1 flex flex-col gap-4 max-w-4xl mx-auto overflow-auto',
                    isReportOpen && 'max-w-3xl',
                  ) }>

                  <ChatHistory
                    taskStore={ taskStore }
                    messageStore={ messageStore }
                    ref={ chatHistoryRef }
                    className="min-h-0 w-full flex-1 p-4"
                    onDeleteMessage={ removeMessage }
                    stateStore={ stateStore }
                  />
                </motion.div>

                {/* 预留悬浮输入框位置 - 目前不显示 */}
                {false && (
                  <div className="absolute bottom-0 left-0 right-0 border-t border-gray-200 bg-white p-4">
                    <div className="mx-auto max-w-4xl">
                      <div className="flex items-center gap-3 border rounded-lg bg-gray-50 p-3">
                        <input
                          type="text"
                          placeholder="Type your message..."
                          className="flex-1 border-none bg-transparent text-gray-700 outline-none placeholder-gray-400"
                        />
                        <button className="h-8 w-8 flex items-center justify-center rounded-full bg-blue-500 text-white transition-colors hover:bg-blue-600">
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
      </div>
    </div>

    <ReportPreview
      isOpen={ isReportOpen }
      onClose={ () => stateStore.isReportOpen = !stateStore.isReportOpen }
      className="flex-shrink-0"
      reportStore={ reportStore }
      taskStore={ taskStore }
      stepState={ stepState }
      mdToCodePreview={ mdToCodePreview }
      stateStore={ stateStore }
    />
  </div>
})

ChatPage.displayName = 'ChatPage'

export type ChatPageProps = {
  className?: string
  style?: React.CSSProperties
  children?: React.ReactNode

  taskStore: TaskStoreType
  stateStore: StateStoreType
  messageStore: MessageStoreType
  mdToCodePreview: MdToCodePreviewType
  resetDistributionStore: () => void
  reportStore: ReportStoreType
  stepState: StepStateType

  // TopBar 相关 props
  showTopBar?: boolean
  topBarAgents?: any[]
  onTopBarAgentClick?: (agent: any) => void
  topBarDropdownExpanded?: boolean
  onTopBarDropdownToggle?: (expanded: boolean) => void

  // ChatV2 流程相关 props
  onStartAIAnalysis?: (formData: any) => Promise<void>
}
& React.DetailedHTMLProps<React.ImgHTMLAttributes<HTMLDivElement>, HTMLDivElement>
