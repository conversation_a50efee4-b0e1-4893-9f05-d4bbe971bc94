import type { ServerOptions } from 'vite'
import { resolve } from 'node:path'
import react from '@vitejs/plugin-react'
// @ts-ignore
import postcssPreset from 'postcss-preset-env'
import { visualizer } from 'rollup-plugin-visualizer'
import UnoCSS from 'unocss/vite'
import AutoImport from 'unplugin-auto-import/vite'
import { defineConfig, loadEnv } from 'vite'
// import mkcert from "vite-plugin-mkcert";
import viteCompression from 'vite-plugin-compression'
import { envParse } from 'vite-plugin-env-parse'
import glsl from 'vite-plugin-glsl'
import svgr from 'vite-plugin-svgr'

const { LOCAL_API_PROXY, LOCAL_API_NODE_PROXY, LOCAL_BUILD_ANALYZE } = loadEnv(
  'development',
  resolve('env'),
  'LOCAL_',
)

const server: ServerOptions = {
  port: 8510,
  host: '0.0.0.0',
  open: false,
  // https: true,
  cors: true,
  proxy: {
    '/api-node': {
      target: LOCAL_API_NODE_PROXY,
      changeOrigin: true,
      secure: false,
    },
    '/api': {
      target: LOCAL_API_PROXY,
      changeOrigin: true,
      secure: false,
      configure: (proxy) => {
        proxy.on('proxyReq', (proxyReq) => {
          // proxyReq.removeHeader("referer");
          proxyReq.removeHeader('origin')
        })
      },
    },
    '/test': {
      target: 'http://*************:5172',
      changeOrigin: true,
      rewrite: path => path.replace(/^\/test/, ''),
    },
  },
}

export default defineConfig(({ mode }) => {
  console.log('vite mode:', mode)
  return {
    optimizeDeps: {
      include: ['@babel/core', '@babel/traverse'] // 强制预构建
    },
    plugins: [
      react({}),
      UnoCSS(),
      // mkcert()
      glsl({
        include: ['**/*.shader'],
      }),
      viteCompression({
        verbose: true,
        disable: false,
        threshold: 10240,
        algorithm: 'gzip',
        ext: '.gz',
        // deleteOriginFile: true, // 源文件压缩后是否删除
      }),
      svgr({
        include: 'src/assets/svg/**/*.svg',
      }),
      envParse(),
      AutoImport({
        imports: ['react', 'react-router-dom'],
        dts: './src/auto-imports.d.ts',
      }),

      // visualizer({
      //   gzipSize: true,
      //   brotliSize: true,
      //   emitFile: false,
      //   filename: "build-analyze.html", //分析图生成的文件名
      //   open: true, //如果存在本地服务端口，将在打包后自动展示
      // }),
    ],

    envDir: 'env',
    resolve: {
      alias: {
        '@': resolve('src'),
      },
    },
    worker: {
      format: 'es',
    },

    css: {
      modules: {
        localsConvention: 'camelCase',
        generateScopedName: '[name]_[local]_[hash:base64:6]',
      },
      preprocessorOptions: {
        less: {
          javascriptEnabled: true,
        },
        scss: {
          additionalData: `
            @use "sass:math";
            @import "@/styles/scss/index.scss";
          `,
        },
      },
      postcss: {
        plugins: [
          postcssPreset({
            autoprefixer: {
              grid: true,
              flexbox: true,
            },
            features: {
              'nesting-rules': true,
            },
          }),
        ],
      },
    },

    esbuild: {
      drop: mode === 'production'
        ? ['console', 'debugger']
        : [],
    },
    build: {
      sourcemap: true,
      rollupOptions: {
        /**
         * 微前端打包
         */
        // preserveEntrySignatures: "allow-extension",
        // input: {
        //   app: "src/main.tsx",
        // },
        output: {
          entryFileNames: '[name]-[hash].js',
          chunkFileNames: 'js/[name]-[hash].js',
          assetFileNames: ({ name }) => {
            return name?.endsWith('.css')
              ? 'css/[name]-[hash].css'
              : 'assets/[name]-[hash].[ext]'
          },
          manualChunks(id) {
            if (id.includes('lottie-web')) {
              return 'lottie-web'
            }
            if (id.includes('three')) {
              return 'three'
            }
            if (id.includes('react-moveable')) {
              return 'react-moveable'
            }
            if (id.includes('html2canvas')) {
              return 'html2canvas'
            }
            if (id.includes('i18next')) {
              return 'i18next'
            }
            if (id.includes('ali-oss')) {
              return 'ali-oss'
            }
            if (id.includes('echarts')) {
              return 'echarts'
            }

            if (id.includes('node_modules')) {
              return 'vendor'
            }
          },
        },
      },
    },
    server,
  }
})
