import type { MarketStep1Params } from '@/api/MarketApi'
import type { ChatProps } from '../types'
import { MarketApi } from '@/api/MarketApi'
import { Loading } from '@/components/Loading'
import { useT } from '@/hooks'
import { memo, useCallback, useRef, useState } from 'react'
import { chatStore } from '../store'
import MainContent from './MainContent'
import TopBar from './TopBar'

export const Chat = memo<ChatProps>(({
  messages,
  onLoadMore,
  setMessages,
  hasMore,
  className,
}) => {
  const t = useT()
  const snap = chatStore.use()
  const [message, setMessage] = useState('')
  const [isProjectExpanded, setIsProjectExpanded] = useState(false)
  const [selectedButton, setSelectedButton] = useState<string | null>(null)
  const [isGenerating, setIsGenerating] = useState(false)
  const [isThinking, setIsThinking] = useState(false)
  const [showForm, setShowForm] = useState(false)
  const [showWorkflow, setShowWorkflow] = useState(false) // 新增工作流状态
  const [uploadedImage, setUploadedImage] = useState<string | null>(null)
  const [formUploadedImage, setFormUploadedImage] = useState<string | null>(null) // 表单独立的图片状态
  const [contentDescription, setContentDescription] = useState('')
  const [formData, setFormData] = useState<Partial<MarketStep1Params>>({
    brand: '',
    product_name: '',
    industry: '',
    competitor: '',
    product: '',
    pic: '',
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formErrors, setFormErrors] = useState<Record<string, string>>({})
  const [thinkingExpanded, setThinkingExpanded] = useState(true)
  const [thinkingMessages, setThinkingMessages] = useState<Array<{ id: string, text: string }>>([]) // 存储thinking消息
  const projectSidebarRef = useRef<any>(null)
  const marketApiRef = useRef(new MarketApi())

  const handleFormSubmit = async () => {
    if (isSubmitting)
      return

    /** 清除之前的错误 */
    setFormErrors({})

    /** 验证必填字段 */
    const errors: Record<string, string> = {}
    const requiredFields = [
      { key: 'brand', label: 'Please enter the content to continue.' },
      { key: 'product_name', label: 'Please enter the content to continue.' },
      { key: 'industry', label: 'Please enter the content to continue.' },
      { key: 'competitor', label: 'Please enter the information to continue.' },
      { key: 'product', label: 'Please enter the information to continue.' },
    ]

    requiredFields.forEach(({ key, label }) => {
      if (!formData[key as keyof MarketStep1Params]?.trim()) {
        errors[key] = `${label.toLowerCase()}`
      }
    })

    if (!formUploadedImage) {
      errors.pic = 'Please upload product image'
    }

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors)
      return
    }

    setIsSubmitting(true)
    try {
      /** 准备API调用参数 */
      const params: MarketStep1Params = {
        industry: formData.industry!,
        industry_id: 83, // 默认行业ID，可以根据需要修改
        product_name: formData.product_name!,
        ip: '专研彩妆', // 默认值，可以根据需要修改
        brand: formData.brand!,
        role: '中国时尚彩妆领导品牌', // 默认值，可以根据需要修改
        product: formData.product!,
        pic: formUploadedImage || '/xxx.png',
        competitor: formData.competitor,
        company: formData.brand, // 使用品牌名作为公司名
      }

      /** 调用API */
      let lastThinkingContent = '' // 记录上次的thinking内容，用于检测变化
      let hasReceivedRealData = false // 标记是否已收到真实数据
      let thinkingMessageBuffer = '' // 用于积累thinking消息内容

      const result = await marketApiRef.current.executeAndStream(
        {
          processInstanceId: '',
          workflowStep: 'step1',
          params,
        },
        (streamResult) => {
          /** 处理流式响应数据 */
          console.log('=== Stream Result ===')
          console.log('streamResult:', streamResult)
          console.log('streamResult.data:', streamResult?.data)
          console.log('streamResult.allJson:', streamResult?.allJson)
          console.log('====================')

          /** 从 result.data 中提取各节点的内容 */
          if (streamResult && streamResult.data) {
            /** 打印所有节点数据以便调试 */
            Object.keys(streamResult.data).forEach((key) => {
              console.log(`节点 [${key}]:`, streamResult.data[key])
            })

            /** 专门处理 thinking 节点 */
            if (streamResult.data.thinking && streamResult.data.thinking.content) {
              const currentContent = streamResult.data.thinking.content
              console.log('Thinking 内容更新:', currentContent)

              /** 如果内容有更新，则更新显示 */
              if (currentContent !== lastThinkingContent) {
                lastThinkingContent = currentContent
                thinkingMessageBuffer = currentContent // 更新缓存的完整内容

                /** 第一次收到真实数据时，清除初始加载消息 */
                if (!hasReceivedRealData) {
                  hasReceivedRealData = true
                  setThinkingMessages([]) // 清空初始消息
                  console.log('首次收到真实数据，清除初始消息')
                }

                /** 实现流式打字机效果 - 逐字符显示 */
                const displayLength = Math.min(currentContent.length, 500) // 限制显示长度
                const displayContent = currentContent.substring(0, displayLength)

                setThinkingMessages([{
                  id: `thinking-stream`,
                  text: displayContent + (currentContent.length > displayLength
                    ? '...'
                    : ''),
                }])

                console.log('更新 thinking 显示内容，长度:', displayContent.length)
              }
            }

            /** 处理其他重要节点的内容并添加到消息列表 */
            const importantNodes = ['brand_name', 'product_name', 'industry_name', 'platform', 'industry_id']
            importantNodes.forEach((nodeTitle) => {
              if (streamResult.data[nodeTitle] && streamResult.data[nodeTitle].content) {
                const content = streamResult.data[nodeTitle].content
                console.log(`检测到 ${nodeTitle} 节点:`, content)

                /** 第一次收到真实数据时，清除初始加载消息 */
                if (!hasReceivedRealData) {
                  hasReceivedRealData = true
                  setThinkingMessages([]) // 清空初始消息
                }

                /** 如果是brand_name等关键信息，可以在thinking消息后面追加显示 */
                if (['brand_name', 'industry_name', 'platform'].includes(nodeTitle)) {
                  setThinkingMessages((prev) => {
                    /** 保留thinking消息，添加新的节点信息 */
                    const thinkingMsg = prev.find(msg => msg.id === 'thinking-stream')
                    const otherMsgs = prev.filter(msg => msg.id !== 'thinking-stream' && !msg.id.startsWith(nodeTitle))

                    const newMsg = {
                      id: `${nodeTitle}-${Date.now()}`,
                      text: `${nodeTitle === 'brand_name'
                        ? '品牌'
                        : nodeTitle === 'industry_name'
                          ? '行业'
                          : '平台'}: ${content}`,
                    }

                    return thinkingMsg
                      ? [thinkingMsg, ...otherMsgs, newMsg]
                      : [...otherMsgs, newMsg]
                  })
                }
              }
            })

            /** 打印最新的 thinkingMessages 状态 */
            console.log('当前 thinkingMessages 数量:', thinkingMessages.length)
          }
        },
      )

      if (!result.hasError) {
        /** 成功后的处理 */
        console.log('API调用成功:', result)
        /** 切换到工作流界面 */
        setShowForm(false)
        setShowWorkflow(true)
      }
      else {
        alert(`处理流式响应数据: ${result.errorMsg}`)
        /** 切换到工作流界面 */
        setShowForm(false)
        setShowWorkflow(true)
      }
    }
    catch (error) {
      console.error('API调用失败:', error)
      alert('网络错误，请稍后重试')
    }
    finally {
      setIsSubmitting(false)
    }
  }

  const handleFormChange = (field: keyof MarketStep1Params, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    /** 清除该字段的错误状态 */
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  /** 点击外部关闭下拉菜单 */
  const handleClickOutside = useCallback((e: MouseEvent) => {
    const target = e.target as HTMLElement
    /** 检查点击是否在下拉菜单外部 */
    if (isProjectExpanded
      && !target.closest('.project-dropdown')
      && !target.closest('.project-toggle-button')) {
      setIsProjectExpanded(false)
    }
  }, [isProjectExpanded])

  return <div
    className="h-full w-full flex flex-col bg-white"
  >
    <Loading loading={ snap.sendLoading }></Loading>

    {/* 顶部栏 - 使用TopBar组件 */}
    <TopBar
      currentProject={ { id: '1', name: 'Project Name Goese Here' } }
      dropdownExpanded={ isProjectExpanded }
      onDropdownToggle={ setIsProjectExpanded }
      setMessages={ setMessages }
      onNewProject={ () => {
        /** 重置所有状态到初始值 */
        setIsGenerating(false)
        setIsThinking(false)
        setShowForm(false)
        setShowWorkflow(false)
        setUploadedImage(null)
        setFormUploadedImage(null)
        setContentDescription('')
        setFormData({
          brand: '',
          product_name: '',
          industry: '',
          competitor: '',
          product: '',
          pic: '',
        })
        setFormErrors({})
        setMessage('')
        setSelectedButton(null)
        setIsProjectExpanded(false)
      } }
    />

    {/* 主内容区 - 使用MainContent组件 */}
    <MainContent
      /** 模式控制 */
      openWorkflow={ snap.openWorkflow }
      isGenerating={ isGenerating }

      // Welcome界面配置
      selectedButton={ selectedButton }
      onButtonSelect={ setSelectedButton }

      /** 输入区域配置 */
      message={ message }
      onMessageChange={ msg => !isGenerating && setMessage(msg) }
      uploadedImage={ uploadedImage }
      onImageUpload={ (file) => {
        const reader = new FileReader()
        reader.onload = (e) => {
          setUploadedImage(e.target?.result as string)
        }
        reader.readAsDataURL(file)
      } }
      onImageRemove={ () => setUploadedImage(null) }
      onGenerate={ () => {
        if (message.trim() && !isGenerating) {
          setContentDescription(message)
          setIsGenerating(true)

          /** 立即显示thinking状态，并设置初始加载消息 */
          setIsThinking(true)
          setThinkingMessages([
            { id: 'init-1', text: '正在连接服务器...' },
            { id: 'init-2', text: '正在分析您的需求...' },
            { id: 'init-3', text: '准备生成内容，请稍候...' },
          ])

          /** 设置超时检测 */
          setTimeout(() => {
            /** 如果30秒后还没有收到真实数据，添加提示消息 */
            setThinkingMessages((prev) => {
              /** 如果还是初始消息（没有收到真实数据） */
              if (prev.every(msg => msg.id.startsWith('init-'))) {
                return [
                  ...prev,
                  { id: 'timeout-1', text: '处理时间较长，请耐心等待...' },
                  { id: 'timeout-2', text: '正在努力生成最佳内容...' },
                ]
              }
              return prev
            })
          }, 30000) // 30秒超时提示
        }
      } }

      // ChatWorkflow配置
      showWorkflow={ showWorkflow }
      showForm={ showForm }
      isThinking={ isThinking }
      thinkingExpanded={ thinkingExpanded }
      onThinkingToggle={ setThinkingExpanded }
      thinkingMessages={ thinkingMessages }
      onThinkingComplete={ () => {
        /** 当thinking完成时，显示表单 */
        setIsThinking(false)
        setShowForm(true)
      } }
      contentDescription={ contentDescription }
      formData={ formData }
      formErrors={ formErrors }
      formUploadedImage={ formUploadedImage }
      isSubmitting={ isSubmitting }
      onFormChange={ handleFormChange }
      onFormSubmit={ handleFormSubmit }
      onFormImageUpload={ (file) => {
        const reader = new FileReader()
        reader.onload = (e) => {
          setFormUploadedImage(e.target?.result as string)
          handleFormChange('pic', e.target?.result as string)
        }
        reader.readAsDataURL(file)
      } }
      onFormImageRemove={ () => {
        setFormUploadedImage(null)
        handleFormChange('pic', '')
      } }

      /** 历史聊天配置 */
      messages={ messages }
      onLoadMore={ onLoadMore }
      hasMore={ hasMore }
      showStep={ snap.showStep }
      enableInput={ snap.enableInput }
      sendButtonText={ t('layout.send') }
    />
  </div>
})
